# Native profile configuration
spring.application.name=sbproto
server.port=9701

# Database Configuration for Native
spring.datasource.url=****************************************
spring.datasource.username=sbproto
spring.datasource.password=sbproto
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration for Native
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Disable Liquibase for native compilation
spring.liquibase.enabled=false

# Native-specific optimizations
spring.jpa.open-in-view=false
spring.jpa.defer-datasource-initialization=true
